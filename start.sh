#!/bin/bash

echo "Vue3 串口通信 Demo 启动脚本"
echo "================================"

# 检查Node.js是否已安装
if ! command -v node &> /dev/null; then
    echo "错误: 未找到Node.js，请先安装Node.js"
    echo "下载地址: https://nodejs.org/"
    exit 1
fi

echo "Node.js已安装，版本:"
node --version

echo ""
echo "检查依赖是否已安装..."
if [ ! -d "node_modules" ]; then
    echo "正在安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "依赖安装失败，请检查网络连接"
        exit 1
    fi
else
    echo "依赖已安装"
fi

echo ""
echo "启动开发服务器..."
echo "注意: 由于Web Serial API的要求，服务器将使用HTTPS"
echo "浏览器可能会显示证书警告，请选择'高级'并'继续访问'"
echo ""

npm run dev
