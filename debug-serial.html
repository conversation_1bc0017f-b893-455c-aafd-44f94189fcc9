<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>串口调试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .content {
            padding: 20px;
        }

        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .section h3 {
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }

        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .log-area {
            height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .input-group input, .input-group select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .input-group input {
            flex: 1;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 串口调试工具</h1>
            <p>专业的串口数据接收和调试工具</p>
        </div>
        
        <div class="content">
            <!-- 连接状态 -->
            <div class="section">
                <h3>连接状态</h3>
                <div id="status" class="status disconnected">未连接</div>
                <div class="controls">
                    <button id="connectBtn" class="btn btn-primary">连接串口</button>
                    <button id="disconnectBtn" class="btn btn-danger" disabled>断开连接</button>
                    <select id="baudRate">
                        <option value="9600">9600</option>
                        <option value="19200">19200</option>
                        <option value="38400">38400</option>
                        <option value="57600">57600</option>
                        <option value="115200">115200</option>
                    </select>
                    <label>波特率</label>
                </div>
            </div>

            <!-- 数据统计 -->
            <div class="section">
                <h3>数据统计</h3>
                <div class="stats">
                    <div class="stat-item">
                        <div id="totalBytes" class="stat-value">0</div>
                        <div class="stat-label">总字节数</div>
                    </div>
                    <div class="stat-item">
                        <div id="totalLines" class="stat-value">0</div>
                        <div class="stat-label">总行数</div>
                    </div>
                    <div class="stat-item">
                        <div id="validData" class="stat-value">0</div>
                        <div class="stat-label">有效数据</div>
                    </div>
                    <div class="stat-item">
                        <div id="dataRate" class="stat-value">0</div>
                        <div class="stat-label">数据速率(B/s)</div>
                    </div>
                </div>
            </div>

            <!-- 原始数据日志 -->
            <div class="section">
                <h3>原始数据日志</h3>
                <div class="controls">
                    <button id="clearLog" class="btn btn-success">清空日志</button>
                    <button id="saveLog" class="btn btn-primary">保存日志</button>
                    <label>
                        <input type="checkbox" id="autoScroll" checked> 自动滚动
                    </label>
                    <label>
                        <input type="checkbox" id="showHex"> 显示十六进制
                    </label>
                </div>
                <div id="logArea" class="log-area"></div>
            </div>

            <!-- 测试功能 -->
            <div class="section">
                <h3>测试功能</h3>
                <div class="input-group">
                    <input type="text" id="testInput" placeholder="输入测试数据，如: sg0001.25kg" value="sg0001.25kg">
                    <button id="sendTest" class="btn btn-success">发送测试</button>
                    <button id="sendRandom" class="btn btn-primary">随机数据</button>
                    <button id="startAuto" class="btn btn-primary">自动测试</button>
                    <button id="stopAuto" class="btn btn-danger">停止测试</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let port = null;
        let reader = null;
        let isConnected = false;
        let totalBytes = 0;
        let totalLines = 0;
        let validData = 0;
        let startTime = Date.now();
        let autoTestInterval = null;

        // DOM元素
        const statusEl = document.getElementById('status');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const baudRateEl = document.getElementById('baudRate');
        const logArea = document.getElementById('logArea');
        const clearLogBtn = document.getElementById('clearLog');
        const saveLogBtn = document.getElementById('saveLog');
        const autoScrollEl = document.getElementById('autoScroll');
        const showHexEl = document.getElementById('showHex');
        const testInput = document.getElementById('testInput');
        const sendTestBtn = document.getElementById('sendTest');
        const sendRandomBtn = document.getElementById('sendRandom');
        const startAutoBtn = document.getElementById('startAuto');
        const stopAutoBtn = document.getElementById('stopAuto');

        // 统计元素
        const totalBytesEl = document.getElementById('totalBytes');
        const totalLinesEl = document.getElementById('totalLines');
        const validDataEl = document.getElementById('validData');
        const dataRateEl = document.getElementById('dataRate');

        // 检查浏览器支持
        if (!('serial' in navigator)) {
            alert('您的浏览器不支持Web Serial API。请使用Chrome 89+或Edge 89+浏览器。');
        }

        // 连接串口
        connectBtn.addEventListener('click', async () => {
            try {
                port = await navigator.serial.requestPort();
                await port.open({
                    baudRate: parseInt(baudRateEl.value),
                    dataBits: 8,
                    stopBits: 1,
                    parity: 'none'
                });

                const textDecoder = new TextDecoderStream();
                const inputDone = port.readable.pipeTo(textDecoder.readable);
                const inputStream = textDecoder.readable;
                reader = inputStream.getReader();

                isConnected = true;
                updateUI();
                readData();
                
                log('✅ 串口连接成功', 'success');
            } catch (error) {
                log(`❌ 连接失败: ${error.message}`, 'error');
            }
        });

        // 断开连接
        disconnectBtn.addEventListener('click', async () => {
            try {
                if (reader) {
                    await reader.cancel();
                    reader = null;
                }
                if (port) {
                    await port.close();
                    port = null;
                }
                isConnected = false;
                updateUI();
                log('🔌 串口已断开', 'info');
            } catch (error) {
                log(`❌ 断开失败: ${error.message}`, 'error');
            }
        });

        // 读取数据
        async function readData() {
            let buffer = '';
            
            try {
                while (isConnected) {
                    const { value, done } = await reader.read();
                    if (done) break;
                    
                    if (value) {
                        totalBytes += value.length;
                        buffer += value;
                        
                        // 记录原始数据
                        logRawData(value);
                        
                        // 处理完整的行
                        const lines = buffer.split(/[\r\n]+/);
                        buffer = lines.pop() || '';
                        
                        for (const line of lines) {
                            if (line.trim()) {
                                totalLines++;
                                processLine(line.trim());
                            }
                        }
                        
                        updateStats();
                    }
                }
            } catch (error) {
                if (isConnected) {
                    log(`❌ 读取数据失败: ${error.message}`, 'error');
                }
            }
        }

        // 处理数据行
        function processLine(line) {
            const timestamp = new Date().toLocaleTimeString();
            
            // 检查是否匹配预期格式
            const match = line.match(/(sg|wg)(\d+\.\d+)kg/i);
            if (match) {
                validData++;
                const type = match[1].toLowerCase();
                const weight = parseFloat(match[2]);
                log(`📊 [${timestamp}] 解析成功: ${type.toUpperCase()} = ${weight}kg (原始: ${line})`, 'success');
            } else {
                log(`📝 [${timestamp}] 原始数据: ${line}`, 'data');
            }
        }

        // 记录原始数据
        function logRawData(data) {
            if (showHexEl.checked) {
                const hex = Array.from(data).map(c => 
                    c.charCodeAt(0).toString(16).padStart(2, '0')
                ).join(' ');
                log(`🔍 HEX: ${hex}`, 'hex');
            }
        }

        // 日志记录
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logArea.textContent += logEntry;
            
            if (autoScrollEl.checked) {
                logArea.scrollTop = logArea.scrollHeight;
            }
        }

        // 更新UI状态
        function updateUI() {
            if (isConnected) {
                statusEl.textContent = '已连接';
                statusEl.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusEl.textContent = '未连接';
                statusEl.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        // 更新统计信息
        function updateStats() {
            totalBytesEl.textContent = totalBytes;
            totalLinesEl.textContent = totalLines;
            validDataEl.textContent = validData;
            
            const elapsed = (Date.now() - startTime) / 1000;
            const rate = Math.round(totalBytes / elapsed);
            dataRateEl.textContent = rate;
        }

        // 测试功能
        sendTestBtn.addEventListener('click', () => {
            const testData = testInput.value.trim();
            if (testData) {
                processLine(testData);
                log(`🧪 测试数据: ${testData}`, 'test');
            }
        });

        sendRandomBtn.addEventListener('click', () => {
            const types = ['sg', 'wg'];
            const type = types[Math.floor(Math.random() * types.length)];
            const weight = (Math.random() * 10).toFixed(2);
            const data = `${type}${weight.padStart(7, '0')}kg`;
            
            processLine(data);
            log(`🎲 随机数据: ${data}`, 'test');
        });

        // 自动测试
        startAutoBtn.addEventListener('click', () => {
            if (autoTestInterval) return;
            
            autoTestInterval = setInterval(() => {
                sendRandomBtn.click();
            }, 1000);
            
            startAutoBtn.disabled = true;
            stopAutoBtn.disabled = false;
            log('🔄 自动测试已启动', 'info');
        });

        stopAutoBtn.addEventListener('click', () => {
            if (autoTestInterval) {
                clearInterval(autoTestInterval);
                autoTestInterval = null;
            }
            
            startAutoBtn.disabled = false;
            stopAutoBtn.disabled = true;
            log('⏹️ 自动测试已停止', 'info');
        });

        // 清空日志
        clearLogBtn.addEventListener('click', () => {
            logArea.textContent = '';
            totalBytes = 0;
            totalLines = 0;
            validData = 0;
            startTime = Date.now();
            updateStats();
        });

        // 保存日志
        saveLogBtn.addEventListener('click', () => {
            const blob = new Blob([logArea.textContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `serial-log-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        });

        // 初始化
        updateUI();
        log('🚀 串口调试工具已启动', 'info');
        log('💡 提示: 请确保在HTTPS环境下使用此工具', 'info');
    </script>
</body>
</html>
