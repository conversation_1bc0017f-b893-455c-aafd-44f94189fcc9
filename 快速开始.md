# 快速开始指南

## 🚀 最简单的使用方法

### 方法1：直接使用测试页面
1. 双击打开 `test.html` 文件
2. 注意：由于Web Serial API的限制，需要通过HTTPS服务器访问才能使用串口功能

### 方法2：使用数据模拟器
1. 双击打开 `demo-data.html` 文件
2. 可以生成模拟的串口数据，用于测试数据解析功能

## 🔧 完整功能使用（需要HTTPS）

### 步骤1：启动HTTPS服务器
由于Web Serial API的安全要求，串口功能必须在HTTPS环境下运行。

**Windows用户：**
```bash
# 双击运行 start.bat
# 或在命令行中运行：
start.bat
```

**Linux/Mac用户：**
```bash
chmod +x start.sh
./start.sh
```

### 步骤2：处理证书警告
1. 浏览器会显示"不安全连接"警告
2. 点击"高级"或"Advanced"
3. 选择"继续前往 localhost（不安全）"或"Proceed to localhost (unsafe)"

### 步骤3：测试串口连接
1. 在页面中点击"连接串口"按钮
2. 选择要连接的串口设备
3. 开始接收和解析数据

## 📊 支持的数据格式

应用支持以下格式的串口数据：
```
sg0000.00kg  // 标准重量数据
wg0000.75kg  // 实际重量数据
wg0001.65kg  // 重量变化数据
```

## 🛠️ 故障排除

### 问题1：无法连接串口
**解决方案：**
- 确保使用Chrome 89+或Edge 89+浏览器
- 必须通过HTTPS访问页面
- 确认串口设备未被其他程序占用

### 问题2：证书错误
**解决方案：**
- 这是正常现象，开发环境使用自签名证书
- 点击"高级"并选择"继续访问"
- 或参考 `HTTPS解决方案.md` 文件

### 问题3：数据无法解析
**解决方案：**
- 检查数据格式是否为 `[sg|wg]XXXX.XXkg`
- 确认串口参数设置正确（波特率等）
- 使用 `demo-data.html` 生成测试数据验证

## 📁 文件说明

| 文件 | 用途 |
|------|------|
| `test.html` | 独立测试页面，包含完整功能 |
| `demo-data.html` | 数据模拟器，生成测试数据 |
| `start.bat` | Windows启动脚本 |
| `start.sh` | Linux/Mac启动脚本 |
| `https-server.js` | 简单HTTPS服务器 |
| `src/App.vue` | Vue3主组件 |
| `HTTPS解决方案.md` | 详细的HTTPS配置指南 |

## 🎯 推荐使用流程

1. **首次使用**：打开 `test.html` 了解界面和功能
2. **数据测试**：使用 `demo-data.html` 生成测试数据
3. **串口测试**：运行HTTPS服务器并连接真实串口设备
4. **开发定制**：修改 `src/App.vue` 添加自定义功能

## 💡 提示

- Web Serial API是现代浏览器的新功能，仅支持Chrome和Edge
- HTTPS要求是浏览器的安全策略，无法绕过
- 自签名证书仅用于开发，生产环境需要有效的SSL证书
- 每次刷新页面后需要重新连接串口设备

## 📞 需要帮助？

如果遇到问题：
1. 查看浏览器控制台的错误信息
2. 参考 `HTTPS解决方案.md` 文件
3. 确认设备和浏览器兼容性
