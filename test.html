<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue3 串口通信 Demo - 测试页面</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
        }
        .app {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .status-section {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-indicator {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            background-color: #f44336;
            color: white;
        }
        .status-indicator.connected {
            background-color: #4caf50;
        }
        .connect-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            background-color: #2196f3;
            color: white;
            cursor: pointer;
            font-size: 16px;
        }
        .connect-btn:hover:not(:disabled) {
            background-color: #1976d2;
        }
        .connect-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .data-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .data-display {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .raw-data, .parsed-data {
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        .raw-data h4, .parsed-data h4 {
            margin: 0;
            padding: 10px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #ddd;
        }
        .data-content {
            height: 300px;
            overflow-y: auto;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .error-message {
            background-color: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #f44336;
            margin-top: 20px;
        }
        .warning-message {
            background-color: #fff3e0;
            color: #ef6c00;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #ff9800;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app">
            <h1>Vue3 串口通信 Demo</h1>
            
            <div class="warning-message">
                <strong>注意：</strong> Web Serial API需要在HTTPS环境下运行，并且需要Chrome 89+或Edge 89+浏览器支持。
                当前页面仅用于测试，实际使用请通过HTTPS访问。
            </div>
            
            <!-- 连接状态 -->
            <div class="status-section">
                <div class="status-indicator" :class="{ connected: isConnected }">
                    {{ isConnected ? '已连接' : '未连接' }}
                </div>
                <button 
                    @click="isConnected ? disconnect() : connect()" 
                    :disabled="isConnecting"
                    class="connect-btn"
                >
                    {{ isConnecting ? '连接中...' : (isConnected ? '断开连接' : '连接串口') }}
                </button>
            </div>

            <!-- 数据显示区域 -->
            <div class="data-section">
                <h3>接收到的数据</h3>
                <div class="data-display">
                    <div class="raw-data">
                        <h4>原始数据:</h4>
                        <div class="data-content">
                            <div v-for="(line, index) in rawDataLines" :key="index">
                                {{ line }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="parsed-data">
                        <h4>解析后的数据:</h4>
                        <div class="data-content">
                            <div v-for="weight in parsedWeights" :key="weight.id" style="margin-bottom: 10px; padding: 5px; background: #f9f9f9; border-radius: 4px;">
                                <strong>{{ weight.type === 'sg' ? '标准重量' : '实际重量' }}:</strong> 
                                {{ weight.value }} kg 
                                <small>({{ weight.time }})</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 错误信息 -->
            <div v-if="errorMessage" class="error-message">
                {{ errorMessage }}
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, nextTick } = Vue;

        createApp({
            setup() {
                const isConnected = ref(false);
                const isConnecting = ref(false);
                const errorMessage = ref('');
                const rawDataLines = ref([]);
                const parsedWeights = ref([]);

                // 串口相关变量
                let port = null;
                let reader = null;

                // 检查浏览器是否支持Web Serial API
                const checkSerialSupport = () => {
                    if (!('serial' in navigator)) {
                        errorMessage.value = '您的浏览器不支持Web Serial API。请使用Chrome 89+或Edge 89+浏览器，并确保在HTTPS环境下运行。';
                        return false;
                    }
                    return true;
                };

                // 连接串口
                const connect = async () => {
                    if (!checkSerialSupport()) return;
                    
                    isConnecting.value = true;
                    errorMessage.value = '';
                    
                    try {
                        // 请求串口权限
                        port = await navigator.serial.requestPort();
                        
                        // 打开串口
                        await port.open({ 
                            baudRate: 9600,
                            dataBits: 8,
                            stopBits: 1,
                            parity: 'none'
                        });
                        
                        // 设置输入流
                        const textDecoder = new TextDecoderStream();
                        const inputDone = port.readable.pipeTo(textDecoder.readable);
                        const inputStream = textDecoder.readable;
                        reader = inputStream.getReader();
                        
                        isConnected.value = true;
                        isConnecting.value = false;
                        
                        // 开始读取数据
                        readData();
                        
                    } catch (error) {
                        console.error('连接串口失败:', error);
                        errorMessage.value = `连接失败: ${error.message}`;
                        isConnecting.value = false;
                    }
                };

                // 断开连接
                const disconnect = async () => {
                    try {
                        if (reader) {
                            await reader.cancel();
                            reader = null;
                        }
                        
                        if (port) {
                            await port.close();
                            port = null;
                        }
                        
                        isConnected.value = false;
                        errorMessage.value = '';
                    } catch (error) {
                        console.error('断开连接失败:', error);
                        errorMessage.value = `断开连接失败: ${error.message}`;
                    }
                };

                // 读取串口数据
                const readData = async () => {
                    let buffer = '';

                    try {
                        console.log('开始读取串口数据...');
                        while (true) {
                            const { value, done } = await reader.read();
                            if (done) {
                                console.log('串口数据流结束');
                                break;
                            }

                            // 调试：显示接收到的原始数据
                            console.log('接收到原始数据:', value, '长度:', value?.length);

                            if (value) {
                                buffer += value;

                                // 调试：显示当前缓冲区内容
                                console.log('当前缓冲区:', buffer);

                                // 尝试多种分割方式
                                let lines = [];

                                // 方式1: 按换行符分割
                                if (buffer.includes('\n')) {
                                    lines = buffer.split('\n');
                                    buffer = lines.pop() || '';
                                }
                                // 方式2: 按回车符分割
                                else if (buffer.includes('\r')) {
                                    lines = buffer.split('\r');
                                    buffer = lines.pop() || '';
                                }
                                // 方式3: 按回车换行符分割
                                else if (buffer.includes('\r\n')) {
                                    lines = buffer.split('\r\n');
                                    buffer = lines.pop() || '';
                                }
                                // 方式4: 如果缓冲区足够长，尝试按固定长度处理
                                else if (buffer.length > 20) {
                                    const possibleLines = buffer.match(/(sg|wg)\d+\.\d+kg/gi);
                                    if (possibleLines) {
                                        lines = possibleLines;
                                        buffer = buffer.replace(/(sg|wg)\d+\.\d+kg/gi, '').trim();
                                    }
                                }

                                // 处理分割出的行
                                for (const line of lines) {
                                    const trimmedLine = line.trim();
                                    if (trimmedLine) {
                                        console.log('处理数据行:', trimmedLine);
                                        processDataLine(trimmedLine);
                                    }
                                }

                                // 如果缓冲区中有完整的数据模式，直接处理
                                const matches = buffer.match(/(sg|wg)\d+\.\d+kg/gi);
                                if (matches) {
                                    for (const match of matches) {
                                        console.log('从缓冲区匹配到数据:', match);
                                        processDataLine(match);
                                        buffer = buffer.replace(match, '').trim();
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        console.error('读取数据失败:', error);
                        if (isConnected.value) {
                            errorMessage.value = `读取数据失败: ${error.message}`;
                        }
                    }
                };

                // 处理单行数据
                const processDataLine = (line) => {
                    // 添加到原始数据显示
                    rawDataLines.value.push(line);
                    if (rawDataLines.value.length > 50) {
                        rawDataLines.value.shift();
                    }
                    
                    // 解析数据格式: sg0000.00kg 或 wg0000.00kg
                    const match = line.match(/^(sg|wg)(\d+\.\d+)kg$/i);
                    if (match) {
                        const type = match[1].toLowerCase();
                        const value = parseFloat(match[2]);
                        const time = new Date().toLocaleTimeString();
                        
                        const weightData = {
                            id: Date.now() + Math.random(),
                            type,
                            value: value.toFixed(2),
                            time,
                            raw: line
                        };
                        
                        // 添加到解析数据
                        parsedWeights.value.unshift(weightData);
                        if (parsedWeights.value.length > 20) {
                            parsedWeights.value.pop();
                        }
                    }
                };

                return {
                    isConnected,
                    isConnecting,
                    errorMessage,
                    rawDataLines,
                    parsedWeights,
                    connect,
                    disconnect
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
