@echo off
echo Vue3 串口通信 Demo 启动脚本
echo ================================

echo 检查Node.js是否已安装...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js已安装，版本:
node --version

echo.
echo 检查依赖是否已安装...
if not exist "node_modules" (
    echo 正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
) else (
    echo 依赖已安装
)

echo.
echo 请选择启动方式:
echo 1. HTTP服务器 (http://localhost:3000) - 用于界面预览
echo 2. HTTPS服务器 (https://localhost:3000) - 用于串口功能测试
echo 3. 简单HTTPS服务器 (https://localhost:8443) - 备用方案
echo 4. 直接打开测试页面
echo.

set /p choice=请输入选择 (1-4):

if "%choice%"=="1" (
    echo 启动HTTP开发服务器...
    echo 注意: HTTP模式下无法使用串口功能
    npm run dev
) else if "%choice%"=="2" (
    echo 启动HTTPS开发服务器...
    echo 浏览器会显示证书警告，请点击"高级"并选择"继续访问"
    npm run dev:https
) else if "%choice%"=="3" (
    echo 启动简单HTTPS服务器...
    echo 浏览器会显示证书警告，请点击"高级"并选择"继续访问"
    node https-server.js
) else if "%choice%"=="4" (
    echo 打开测试页面...
    start test.html
    echo 注意: 需要通过HTTPS服务器访问才能使用串口功能
) else (
    echo 无效选择，默认启动HTTP服务器
    npm run dev
)

pause
