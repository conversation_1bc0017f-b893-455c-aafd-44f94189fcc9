@echo off
echo Vue3 串口通信 Demo 启动脚本
echo ================================

echo 检查Node.js是否已安装...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js已安装，版本:
node --version

echo.
echo 检查依赖是否已安装...
if not exist "node_modules" (
    echo 正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
) else (
    echo 依赖已安装
)

echo.
echo 启动开发服务器...
echo 注意: 由于Web Serial API的要求，服务器将使用HTTPS
echo 浏览器可能会显示证书警告，请选择"高级"并"继续访问"
echo.

npm run dev

pause
