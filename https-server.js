const https = require('https');
const fs = require('fs');
const path = require('path');
const url = require('url');

// 简单的自签名证书（仅用于开发）
const key = `-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB
wEiOfH3nzor9cwHXLbkiG+cy6vJ3oCSXofI1aMcVGxdxPkfHz0VjHQeCeHGizdDt
n6c0wGjdNBMWzlTI4AITh/LSpiAloc78zPTdw7jcI02kbVEiGFiOr5HpOuAceqe
iqNuB0aP0A5LSUr8BDrHCCHxtF50VHVsx9zIReCkGDdkUwwzcsGgElJz6dVXqe7
zH/rKiU5WuqDhieCMuFlaZdqHdqxvqz1YqaHjdjfetqykwXmpFgvgZyCzDqUeVw
s6HgbOZq2oBHyDQajZRMEBBBK+wjWMoMYAGFfNg1uSb9ELWEeOhXBIb2zApK+V
VmwVDyeDJcIrAgMBAAECggEBALc2lQACC8cSfh+ozSSqBQ9K74s06eJoNx3DqIww
CSDqMxDqHmFxHskjfuLJzq3tCM+2qRworYCbHWZ5dc2mq4jOx6dkjMgCxHkRBhOP
B9g3lys4CH6Lw1OF+FMuj4cwtgtk05ycjpwz8xxZv+v6V4XXgAqMqJHGK+Yk8f+s
f0dg25NEg6TYmz0Dkh7jZAHVoGEMaASdvBmHZiDWw00q3snuOej4YSg5W9QJ1OIA
2RXKjw1fipFMl6cOkr+NqSdnxnppZZClgzDBO2lqRy0lyXMtojw45CQXxsxVd6W8
kNBu1aJ9nVHDk5UEFMpn3+q3+u+9PK4R8alpOqoMDcACL+ECgYEA2W7K8fb4ON6T
6bsqkQFBKaxHhP7ZcqBh7sFj03VcqOzd4HB68XBDKw+g/sQqMFigGDYhZB61ZdHI
kAHlqTzLx4fMfBg8PlMAqJwM5VGmBDPkmhJmhFHVPH+x550WXdC+N9H7tAPmYsrw
+YhkNzDoBcjcQiuDySdUuScaUiGjyvECgYEA3AhqWyihxIvfk2Z6j4JGLjgZl755
xMtdgOBiAu4uEwhklWotmRoikn+TiDyiHFnxlBntjc7w5di9N2GlVj+hWFY5cSs6
eWDODlHbDA6ckeLFwbm2y1W1Haan4f/UiATXvFOX16y1D2omgtxjeRf3hvGU0+l3
wNqtrA5FUC3k1Bw=
-----END PRIVATE KEY-----`;

const cert = `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAJC1HiIAZAiIMA0GCSqGSIb3DQEBBQUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMTExMjMxMDg1OTU5WhcNMTIxMjMwMDg1OTU5WjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAu1SU1L7VLPHCgcBIjnx9586K/XMB1y25IhvnMuryd6Akl6HyNWjHFRsX
cT5Hx89FYx0Hgnhxos3Q7Z+nNMBo3TQTFs5UyOACE4fy0qYgJaHO/Mz03cO43CNN
pG1RIhhYjq+R6TrgHHqnoqjbgdGj9AOS0lK/AQ6xwgh8bRedFR1bMfcyEXgpBg3Z
FMMc3LBoBJSc+nVV6nu8x/6yolOVrqg4YngjLhZWmXah3asb6s9WKmh43Y33rass
MF5qRYL4GcgswqlHlcLOh4GzmatqAR8g0Go2UTBAQQSvsI1jKDGABhXzYNbkm/RC
1hHjoVwSG9swKSvlVZsFQ8ngyXCKwIDAQABo1AwTjAdBgNVHQ4EFgQUU3m/WqorS
s9UgOHYm8Cd8rIDZsswHwYDVR0jBBgwFoAUU3m/WqorSs9UgOHYm8Cd8rIDZssw
DAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQUFAAOCAQEAWjsHVQQqm5P5+u/y+ctH
fs+F6netKJEP9SRhyovV2oj8kHRc9kZvslENjRIs1NKRGXUIQ8OJMVUkSBjSrpd5
E15p0+jFwdVqQk+TtMg4j3XrxgTrjmwU5u7NsWaAB9s+2+5qnfMfEvyJBTl/BHd3
R4Y8NTAuBuqaVPQFJEDFx7KrUlQIBDEWLlY1BWouWC9aO/+NbU69+9QHhZAsL6Ou
T6/KXa+oK9FlFedR/W1csEGc3dcYkHp+B0pFLrCQ4G9hcDgJwJDPRaFDMsrB/hgJ
hXEFAX3xMTKs2e+24kjPaCUkMiPSCHVppBgF/VtSWYMA3/GN2i2a78dNtl+h6lYo
0g==
-----END CERTIFICATE-----`;

const options = {
  key: key,
  cert: cert
};

const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.wav': 'audio/wav',
  '.mp4': 'video/mp4',
  '.woff': 'application/font-woff',
  '.ttf': 'application/font-ttf',
  '.eot': 'application/vnd.ms-fontobject',
  '.otf': 'application/font-otf',
  '.wasm': 'application/wasm'
};

const server = https.createServer(options, (req, res) => {
  const parsedUrl = url.parse(req.url);
  let pathname = `.${parsedUrl.pathname}`;
  
  // 默认文件
  if (pathname === './') {
    pathname = './test.html';
  }

  const ext = path.parse(pathname).ext;
  const mimeType = mimeTypes[ext] || 'text/plain';

  fs.readFile(pathname, (err, data) => {
    if (err) {
      res.statusCode = 404;
      res.setHeader('Content-Type', 'text/plain');
      res.end('File not found');
    } else {
      res.statusCode = 200;
      res.setHeader('Content-Type', mimeType);
      res.end(data);
    }
  });
});

const PORT = 8443;
server.listen(PORT, () => {
  console.log(`HTTPS Server running at https://localhost:${PORT}/`);
  console.log('注意: 浏览器会显示证书警告，请点击"高级"并选择"继续访问"');
  console.log('这是开发用的自签名证书，在生产环境中请使用有效的SSL证书');
});
