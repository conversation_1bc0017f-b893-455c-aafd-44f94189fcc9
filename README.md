# Vue3 串口通信 Demo

这是一个使用Vue3和Web Serial API实现的串口通信演示项目，可以连接串口设备并实时接收和解析数据。

## 功能特性

- 🔌 支持串口连接和断开
- 📊 实时显示接收到的原始数据
- 🔍 自动解析串口数据格式（sg/wg + 重量值）
- 📈 统计信息显示
- 📱 响应式设计，支持移动端

## 支持的数据格式

项目支持解析以下格式的串口数据：
```
sg0000.00kg  // 标准重量数据
wg0000.75kg  // 实际重量数据
```

## 浏览器要求

由于使用了Web Serial API，需要以下浏览器支持：
- Chrome 89+
- Edge 89+
- 其他基于Chromium的浏览器

**注意：** 必须在HTTPS环境下运行才能使用Web Serial API。

## 快速开始

### 方法1：使用测试页面（推荐用于快速测试）

1. 直接在浏览器中打开 `test.html` 文件
2. 注意：由于Web Serial API的限制，需要在HTTPS环境下才能正常使用串口功能

### 方法2：使用Vite开发服务器（完整功能）

1. 安装依赖：
```bash
npm install
```

2. 启动开发服务器：
```bash
npm run dev
```

3. 在浏览器中访问显示的HTTPS地址（通常是 `https://localhost:3000`）

### 方法3：使用HTTPS服务器

如果需要在生产环境中使用，需要配置HTTPS服务器。可以使用以下方法之一：

1. 使用nginx或apache配置HTTPS
2. 使用Node.js的https模块
3. 使用云服务提供商的HTTPS支持

## 使用说明

1. 确保在HTTPS环境下访问页面
2. 点击"连接串口"按钮
3. 在弹出的对话框中选择要连接的串口设备
4. 连接成功后，应用会自动开始接收和显示数据
5. 可以在界面上看到：
   - 原始数据流
   - 解析后的重量数据
   - 统计信息

## 串口配置

- 默认波特率：9600
- 数据位：8
- 停止位：1
- 校验位：无

可以在连接前修改波特率设置。

## 项目结构

```
├── src/
│   ├── App.vue          # 主组件
│   └── main.js          # 入口文件
├── index.html           # HTML模板
├── test.html            # 测试页面（独立运行）
├── package.json         # 项目配置
├── vite.config.js       # Vite配置
└── README.md           # 说明文档
```

## 技术栈

- Vue 3 (Composition API)
- Vite
- Web Serial API
- CSS Grid/Flexbox

## 数据解析逻辑

应用会自动解析符合以下格式的串口数据：
- `sg` 开头：标准重量数据
- `wg` 开头：实际重量数据
- 格式：`[sg|wg]数字.数字kg`

例如：
```
sg0000.00kg  -> 标准重量: 0.00kg
wg0001.65kg  -> 实际重量: 1.65kg
```

## 故障排除

### 1. 无法连接串口
- 确保浏览器支持Web Serial API（Chrome 89+或Edge 89+）
- 确保在HTTPS环境下运行
- 检查串口设备是否正确连接
- 确认串口没有被其他应用程序占用

### 2. 数据无法解析
- 检查串口数据格式是否符合预期
- 确认波特率设置正确
- 检查串口参数（数据位、停止位、校验位）

### 3. HTTPS证书问题
- 开发环境中，浏览器可能会显示证书警告，点击"高级"并选择"继续访问"
- 生产环境中，需要使用有效的SSL证书

## 注意事项

1. Web Serial API需要用户手动授权才能访问串口
2. 必须在HTTPS环境下运行
3. 串口数据格式必须符合预定义的格式才能正确解析
4. 建议使用Chrome或Edge浏览器以获得最佳体验
5. 每次刷新页面后需要重新连接串口设备
