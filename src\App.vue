<template>
  <div class="app">
    <h1>Vue3 串口通信 Demo</h1>
    
    <!-- 连接状态 -->
    <div class="status-section">
      <div class="status-indicator" :class="{ connected: isConnected }">
        {{ isConnected ? '已连接' : '未连接' }}
      </div>
      <button 
        @click="isConnected ? disconnect() : connect()" 
        :disabled="isConnecting"
        class="connect-btn"
      >
        {{ isConnecting ? '连接中...' : (isConnected ? '断开连接' : '连接串口') }}
      </button>
    </div>

    <!-- 串口配置 -->
    <div class="config-section" v-if="!isConnected">
      <h3>串口配置</h3>
      <div class="config-item">
        <label>波特率:</label>
        <select v-model="baudRate">
          <option value="9600">9600</option>
          <option value="19200">19200</option>
          <option value="38400">38400</option>
          <option value="57600">57600</option>
          <option value="115200">115200</option>
        </select>
      </div>
    </div>

    <!-- 测试数据区域 -->
    <div class="test-section">
      <h3>测试功能</h3>
      <div class="test-controls">
        <input
          v-model="testData"
          placeholder="输入测试数据，如: sg0001.25kg"
          class="test-input"
        />
        <button @click="sendTestData" class="test-btn">发送测试数据</button>
        <button @click="sendRandomData" class="test-btn">发送随机数据</button>
      </div>
      <div class="test-info">
        <small>用于测试数据解析功能，无需连接真实串口设备</small>
      </div>
    </div>

    <!-- 数据显示区域 -->
    <div class="data-section">
      <h3>接收到的数据</h3>
      <div class="data-display">
        <div class="raw-data">
          <h4>原始数据:</h4>
          <div class="data-content" ref="rawDataRef">
            <div v-for="(line, index) in rawDataLines" :key="index" class="data-line">
              {{ line }}
            </div>
          </div>
        </div>
        
        <div class="parsed-data">
          <h4>解析后的数据:</h4>
          <div class="weight-display">
            <div class="weight-item" v-for="weight in parsedWeights" :key="weight.id">
              <span class="weight-type">{{ weight.type === 'sg' ? '标准重量' : '实际重量' }}:</span>
              <span class="weight-value">{{ weight.value }} kg</span>
              <span class="weight-time">{{ weight.time }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <h3>统计信息</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-label">接收数据条数:</span>
          <span class="stat-value">{{ totalDataCount }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">最新标准重量:</span>
          <span class="stat-value">{{ latestSgWeight }} kg</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">最新实际重量:</span>
          <span class="stat-value">{{ latestWgWeight }} kg</span>
        </div>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'

// 响应式数据
const isConnected = ref(false)
const isConnecting = ref(false)
const errorMessage = ref('')
const baudRate = ref(9600)
const rawDataLines = ref([])
const parsedWeights = ref([])
const totalDataCount = ref(0)
const latestSgWeight = ref('0.00')
const latestWgWeight = ref('0.00')
const testData = ref('sg0001.25kg')

// DOM引用
const rawDataRef = ref(null)

// 串口相关变量
let port = null
let reader = null
let inputDone = null
let outputDone = null
let inputStream = null
let outputStream = null

// 检查浏览器是否支持Web Serial API
const checkSerialSupport = () => {
  if (!('serial' in navigator)) {
    errorMessage.value = '您的浏览器不支持Web Serial API。请使用Chrome 89+或Edge 89+浏览器。'
    return false
  }
  return true
}

// 连接串口
const connect = async () => {
  if (!checkSerialSupport()) return
  
  isConnecting.value = true
  errorMessage.value = ''
  
  try {
    // 请求串口权限
    port = await navigator.serial.requestPort()
    
    // 打开串口
    await port.open({ 
      baudRate: baudRate.value,
      dataBits: 8,
      stopBits: 1,
      parity: 'none'
    })
    
    // 设置输入流
    const textDecoder = new TextDecoderStream()
    inputDone = port.readable.pipeTo(textDecoder.readable)
    inputStream = textDecoder.readable
    reader = inputStream.getReader()
    
    isConnected.value = true
    isConnecting.value = false
    
    // 开始读取数据
    readData()
    
  } catch (error) {
    console.error('连接串口失败:', error)
    errorMessage.value = `连接失败: ${error.message}`
    isConnecting.value = false
  }
}

// 断开连接
const disconnect = async () => {
  try {
    if (reader) {
      await reader.cancel()
      await inputDone.catch(() => {})
      reader = null
      inputDone = null
    }
    
    if (port) {
      await port.close()
      port = null
    }
    
    isConnected.value = false
    errorMessage.value = ''
  } catch (error) {
    console.error('断开连接失败:', error)
    errorMessage.value = `断开连接失败: ${error.message}`
  }
}

// 读取串口数据
const readData = async () => {
  let buffer = ''

  try {
    console.log('开始读取串口数据...')
    while (true) {
      const { value, done } = await reader.read()
      if (done) {
        console.log('串口数据流结束')
        break
      }

      // 调试：显示接收到的原始数据
      console.log('接收到原始数据:', value, '长度:', value?.length)

      if (value) {
        buffer += value

        // 调试：显示当前缓冲区内容
        console.log('当前缓冲区:', buffer)

        // 尝试多种分割方式
        let lines = []

        // 方式1: 按换行符分割
        if (buffer.includes('\n')) {
          lines = buffer.split('\n')
          buffer = lines.pop() || ''
        }
        // 方式2: 按回车符分割
        else if (buffer.includes('\r')) {
          lines = buffer.split('\r')
          buffer = lines.pop() || ''
        }
        // 方式3: 按回车换行符分割
        else if (buffer.includes('\r\n')) {
          lines = buffer.split('\r\n')
          buffer = lines.pop() || ''
        }
        // 方式4: 如果缓冲区足够长，尝试按固定长度处理
        else if (buffer.length > 20) {
          // 假设每条数据大约10-15个字符，如果缓冲区很长可能包含多条数据
          const possibleLines = buffer.match(/(sg|wg)\d+\.\d+kg/gi)
          if (possibleLines) {
            lines = possibleLines
            buffer = buffer.replace(/(sg|wg)\d+\.\d+kg/gi, '').trim()
          }
        }

        // 处理分割出的行
        for (const line of lines) {
          const trimmedLine = line.trim()
          if (trimmedLine) {
            console.log('处理数据行:', trimmedLine)
            processDataLine(trimmedLine)
          }
        }

        // 如果缓冲区中有完整的数据模式，直接处理
        const matches = buffer.match(/(sg|wg)\d+\.\d+kg/gi)
        if (matches) {
          for (const match of matches) {
            console.log('从缓冲区匹配到数据:', match)
            processDataLine(match)
            buffer = buffer.replace(match, '').trim()
          }
        }
      }
    }
  } catch (error) {
    console.error('读取数据失败:', error)
    if (isConnected.value) {
      errorMessage.value = `读取数据失败: ${error.message}`
    }
  }
}

// 处理单行数据
const processDataLine = (line) => {
  console.log('处理数据行:', line, '长度:', line.length)

  // 添加到原始数据显示（显示所有接收到的数据，包括不匹配的）
  const timestamp = new Date().toLocaleTimeString()
  const displayLine = `[${timestamp}] ${line}`
  rawDataLines.value.push(displayLine)
  if (rawDataLines.value.length > 100) {
    rawDataLines.value.shift() // 保持最新100条数据
  }

  // 滚动到底部
  nextTick(() => {
    if (rawDataRef.value) {
      rawDataRef.value.scrollTop = rawDataRef.value.scrollHeight
    }
  })

  // 尝试多种正则表达式匹配
  let match = null

  // 标准格式: sg0000.00kg 或 wg0000.00kg
  match = line.match(/^(sg|wg)(\d+\.\d+)kg$/i)

  // 如果标准格式不匹配，尝试更宽松的格式
  if (!match) {
    match = line.match(/(sg|wg)(\d+\.\d+)kg/i) // 不要求开头结尾
  }

  // 更宽松的格式，允许空格
  if (!match) {
    match = line.match(/\s*(sg|wg)\s*(\d+\.\d+)\s*kg\s*/i)
  }

  // 十六进制或其他编码的数据
  if (!match && line.length > 0) {
    console.log('未匹配的数据，十六进制:', Array.from(line).map(c => c.charCodeAt(0).toString(16)).join(' '))
  }

  if (match) {
    console.log('成功匹配数据:', match)
    const type = match[1].toLowerCase()
    const value = parseFloat(match[2])
    const time = new Date().toLocaleTimeString()

    const weightData = {
      id: Date.now() + Math.random(),
      type,
      value: value.toFixed(2),
      time,
      raw: line
    }

    // 添加到解析数据
    parsedWeights.value.unshift(weightData)
    if (parsedWeights.value.length > 50) {
      parsedWeights.value.pop() // 保持最新50条解析数据
    }

    // 更新统计信息
    totalDataCount.value++
    if (type === 'sg') {
      latestSgWeight.value = value.toFixed(2)
    } else if (type === 'wg') {
      latestWgWeight.value = value.toFixed(2)
    }

    console.log('解析成功:', weightData)
  } else {
    console.log('数据格式不匹配:', line)
  }
}

// 发送测试数据
const sendTestData = () => {
  if (testData.value.trim()) {
    console.log('发送测试数据:', testData.value)
    processDataLine(testData.value.trim())
  }
}

// 发送随机测试数据
const sendRandomData = () => {
  const types = ['sg', 'wg']
  const type = types[Math.floor(Math.random() * types.length)]
  const weight = (Math.random() * 10).toFixed(2)
  const randomData = `${type}${weight.padStart(7, '0')}kg`

  console.log('发送随机数据:', randomData)
  processDataLine(randomData)
}

// 组件卸载时清理
onUnmounted(() => {
  if (isConnected.value) {
    disconnect()
  }
})
</script>

<style scoped>
.app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.status-section {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-indicator {
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  background-color: #f44336;
  color: white;
}

.status-indicator.connected {
  background-color: #4caf50;
}

.connect-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  background-color: #2196f3;
  color: white;
  cursor: pointer;
  font-size: 16px;
}

.connect-btn:hover:not(:disabled) {
  background-color: #1976d2;
}

.connect-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.config-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.config-item label {
  font-weight: bold;
  min-width: 80px;
}

.config-item select {
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* 测试区域样式 */
.test-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.test-controls {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;
}

.test-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.test-btn {
  padding: 8px 16px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.test-btn:hover {
  background: #218838;
}

.test-info {
  color: #666;
  font-size: 12px;
}

.data-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.data-display {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.raw-data, .parsed-data {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.raw-data h4, .parsed-data h4 {
  margin: 0;
  padding: 10px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.data-content {
  height: 300px;
  overflow-y: auto;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.data-line {
  padding: 2px 0;
  border-bottom: 1px solid #eee;
}

.weight-display {
  height: 300px;
  overflow-y: auto;
  padding: 10px;
}

.weight-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  margin-bottom: 5px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.weight-type {
  font-weight: bold;
  color: #666;
}

.weight-value {
  font-size: 18px;
  font-weight: bold;
  color: #2196f3;
}

.weight-time {
  font-size: 12px;
  color: #999;
}

.stats-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.stat-label {
  font-weight: bold;
  color: #666;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #4caf50;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #f44336;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .data-display {
    grid-template-columns: 1fr;
  }
  
  .status-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
