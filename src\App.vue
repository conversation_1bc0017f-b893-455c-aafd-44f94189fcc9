<template>
  <div class="app">
    <h1>Vue3 串口通信 Demo</h1>
    
    <!-- 连接状态 -->
    <div class="status-section">
      <div class="status-indicator" :class="{ connected: isConnected }">
        {{ isConnected ? '已连接' : '未连接' }}
      </div>
      <button 
        @click="isConnected ? disconnect() : connect()" 
        :disabled="isConnecting"
        class="connect-btn"
      >
        {{ isConnecting ? '连接中...' : (isConnected ? '断开连接' : '连接串口') }}
      </button>
    </div>

    <!-- 串口配置 -->
    <div class="config-section" v-if="!isConnected">
      <h3>串口配置</h3>
      <div class="config-item">
        <label>波特率:</label>
        <select v-model="baudRate">
          <option value="9600">9600</option>
          <option value="19200">19200</option>
          <option value="38400">38400</option>
          <option value="57600">57600</option>
          <option value="115200">115200</option>
        </select>
      </div>
    </div>

    <!-- 数据显示区域 -->
    <div class="data-section">
      <h3>接收到的数据</h3>
      <div class="data-display">
        <div class="raw-data">
          <h4>原始数据:</h4>
          <div class="data-content" ref="rawDataRef">
            <div v-for="(line, index) in rawDataLines" :key="index" class="data-line">
              {{ line }}
            </div>
          </div>
        </div>
        
        <div class="parsed-data">
          <h4>解析后的数据:</h4>
          <div class="weight-display">
            <div class="weight-item" v-for="weight in parsedWeights" :key="weight.id">
              <span class="weight-type">{{ weight.type === 'sg' ? '标准重量' : '实际重量' }}:</span>
              <span class="weight-value">{{ weight.value }} kg</span>
              <span class="weight-time">{{ weight.time }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <h3>统计信息</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-label">接收数据条数:</span>
          <span class="stat-value">{{ totalDataCount }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">最新标准重量:</span>
          <span class="stat-value">{{ latestSgWeight }} kg</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">最新实际重量:</span>
          <span class="stat-value">{{ latestWgWeight }} kg</span>
        </div>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'

// 响应式数据
const isConnected = ref(false)
const isConnecting = ref(false)
const errorMessage = ref('')
const baudRate = ref(9600)
const rawDataLines = ref([])
const parsedWeights = ref([])
const totalDataCount = ref(0)
const latestSgWeight = ref('0.00')
const latestWgWeight = ref('0.00')

// DOM引用
const rawDataRef = ref(null)

// 串口相关变量
let port = null
let reader = null
let inputDone = null
let outputDone = null
let inputStream = null
let outputStream = null

// 检查浏览器是否支持Web Serial API
const checkSerialSupport = () => {
  if (!('serial' in navigator)) {
    errorMessage.value = '您的浏览器不支持Web Serial API。请使用Chrome 89+或Edge 89+浏览器。'
    return false
  }
  return true
}

// 连接串口
const connect = async () => {
  if (!checkSerialSupport()) return
  
  isConnecting.value = true
  errorMessage.value = ''
  
  try {
    // 请求串口权限
    port = await navigator.serial.requestPort()
    
    // 打开串口
    await port.open({ 
      baudRate: baudRate.value,
      dataBits: 8,
      stopBits: 1,
      parity: 'none'
    })
    
    // 设置输入流
    const textDecoder = new TextDecoderStream()
    inputDone = port.readable.pipeTo(textDecoder.readable)
    inputStream = textDecoder.readable
    reader = inputStream.getReader()
    
    isConnected.value = true
    isConnecting.value = false
    
    // 开始读取数据
    readData()
    
  } catch (error) {
    console.error('连接串口失败:', error)
    errorMessage.value = `连接失败: ${error.message}`
    isConnecting.value = false
  }
}

// 断开连接
const disconnect = async () => {
  try {
    if (reader) {
      await reader.cancel()
      await inputDone.catch(() => {})
      reader = null
      inputDone = null
    }
    
    if (port) {
      await port.close()
      port = null
    }
    
    isConnected.value = false
    errorMessage.value = ''
  } catch (error) {
    console.error('断开连接失败:', error)
    errorMessage.value = `断开连接失败: ${error.message}`
  }
}

// 读取串口数据
const readData = async () => {
  let buffer = ''
  
  try {
    while (true) {
      const { value, done } = await reader.read()
      if (done) break
      
      buffer += value
      
      // 按行分割数据
      const lines = buffer.split('\n')
      buffer = lines.pop() || '' // 保留最后一个不完整的行
      
      for (const line of lines) {
        const trimmedLine = line.trim()
        if (trimmedLine) {
          processDataLine(trimmedLine)
        }
      }
    }
  } catch (error) {
    console.error('读取数据失败:', error)
    if (isConnected.value) {
      errorMessage.value = `读取数据失败: ${error.message}`
    }
  }
}

// 处理单行数据
const processDataLine = (line) => {
  // 添加到原始数据显示
  rawDataLines.value.push(line)
  if (rawDataLines.value.length > 100) {
    rawDataLines.value.shift() // 保持最新100条数据
  }
  
  // 滚动到底部
  nextTick(() => {
    if (rawDataRef.value) {
      rawDataRef.value.scrollTop = rawDataRef.value.scrollHeight
    }
  })
  
  // 解析数据格式: sg0000.00kg 或 wg0000.00kg
  const match = line.match(/^(sg|wg)(\d+\.\d+)kg$/i)
  if (match) {
    const type = match[1].toLowerCase()
    const value = parseFloat(match[2])
    const time = new Date().toLocaleTimeString()
    
    const weightData = {
      id: Date.now() + Math.random(),
      type,
      value: value.toFixed(2),
      time,
      raw: line
    }
    
    // 添加到解析数据
    parsedWeights.value.unshift(weightData)
    if (parsedWeights.value.length > 50) {
      parsedWeights.value.pop() // 保持最新50条解析数据
    }
    
    // 更新统计信息
    totalDataCount.value++
    if (type === 'sg') {
      latestSgWeight.value = value.toFixed(2)
    } else if (type === 'wg') {
      latestWgWeight.value = value.toFixed(2)
    }
  }
}

// 组件卸载时清理
onUnmounted(() => {
  if (isConnected.value) {
    disconnect()
  }
})
</script>

<style scoped>
.app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

.status-section {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-indicator {
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  background-color: #f44336;
  color: white;
}

.status-indicator.connected {
  background-color: #4caf50;
}

.connect-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  background-color: #2196f3;
  color: white;
  cursor: pointer;
  font-size: 16px;
}

.connect-btn:hover:not(:disabled) {
  background-color: #1976d2;
}

.connect-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.config-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.config-item label {
  font-weight: bold;
  min-width: 80px;
}

.config-item select {
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.data-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.data-display {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.raw-data, .parsed-data {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.raw-data h4, .parsed-data h4 {
  margin: 0;
  padding: 10px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.data-content {
  height: 300px;
  overflow-y: auto;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.data-line {
  padding: 2px 0;
  border-bottom: 1px solid #eee;
}

.weight-display {
  height: 300px;
  overflow-y: auto;
  padding: 10px;
}

.weight-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  margin-bottom: 5px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.weight-type {
  font-weight: bold;
  color: #666;
}

.weight-value {
  font-size: 18px;
  font-weight: bold;
  color: #2196f3;
}

.weight-time {
  font-size: 12px;
  color: #999;
}

.stats-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.stat-label {
  font-weight: bold;
  color: #666;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #4caf50;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #f44336;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .data-display {
    grid-template-columns: 1fr;
  }
  
  .status-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
