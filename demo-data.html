<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>串口数据模拟器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .control-section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .control-group {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        .control-group:last-child {
            margin-bottom: 0;
        }
        label {
            font-weight: bold;
            min-width: 120px;
        }
        input, select, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #2196f3;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #1976d2;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .data-output {
            background-color: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 4px;
            height: 400px;
            overflow-y: auto;
            margin-top: 20px;
            border: 2px solid #333;
        }
        .data-line {
            margin-bottom: 5px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .status.running {
            background-color: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .status.stopped {
            background-color: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info-box {
            background-color: #e3f2fd;
            color: #1565c0;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>串口数据模拟器</h1>
        
        <div class="info-box">
            <strong>说明：</strong> 这个工具可以模拟生成串口数据，用于测试Vue3串口通信Demo的数据解析功能。
            生成的数据格式与实际串口设备发送的数据格式相同。
        </div>

        <div class="status" id="status" class="stopped">
            状态: 已停止
        </div>

        <div class="control-section">
            <h3>数据生成设置</h3>
            
            <div class="control-group">
                <label>数据类型:</label>
                <select id="dataType">
                    <option value="mixed">混合数据 (sg + wg)</option>
                    <option value="sg">仅标准重量 (sg)</option>
                    <option value="wg">仅实际重量 (wg)</option>
                </select>
            </div>

            <div class="control-group">
                <label>重量范围:</label>
                <input type="number" id="minWeight" value="0" step="0.01" min="0" max="9999">
                <span>到</span>
                <input type="number" id="maxWeight" value="10" step="0.01" min="0" max="9999">
                <span>kg</span>
            </div>

            <div class="control-group">
                <label>发送间隔:</label>
                <input type="number" id="interval" value="1000" min="100" max="10000">
                <span>毫秒</span>
            </div>

            <div class="control-group">
                <label>数据变化:</label>
                <select id="changeMode">
                    <option value="random">随机变化</option>
                    <option value="increment">递增</option>
                    <option value="fixed">固定值</option>
                </select>
            </div>

            <div class="control-group">
                <button id="startBtn" onclick="startGeneration()">开始生成</button>
                <button id="stopBtn" onclick="stopGeneration()" disabled>停止生成</button>
                <button onclick="clearOutput()">清空输出</button>
                <button onclick="copyOutput()">复制数据</button>
            </div>
        </div>

        <div class="control-section">
            <h3>生成的数据</h3>
            <div class="data-output" id="dataOutput"></div>
        </div>
    </div>

    <script>
        let generationInterval = null;
        let currentWeight = 0;
        let dataCount = 0;

        function startGeneration() {
            const dataType = document.getElementById('dataType').value;
            const minWeight = parseFloat(document.getElementById('minWeight').value);
            const maxWeight = parseFloat(document.getElementById('maxWeight').value);
            const interval = parseInt(document.getElementById('interval').value);
            const changeMode = document.getElementById('changeMode').value;

            if (minWeight >= maxWeight) {
                alert('最小重量必须小于最大重量');
                return;
            }

            currentWeight = minWeight;
            dataCount = 0;

            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('status').textContent = '状态: 正在生成数据...';
            document.getElementById('status').className = 'status running';

            generationInterval = setInterval(() => {
                generateData(dataType, minWeight, maxWeight, changeMode);
            }, interval);
        }

        function stopGeneration() {
            if (generationInterval) {
                clearInterval(generationInterval);
                generationInterval = null;
            }

            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('status').textContent = '状态: 已停止';
            document.getElementById('status').className = 'status stopped';
        }

        function generateData(dataType, minWeight, maxWeight, changeMode) {
            let weight;
            
            switch (changeMode) {
                case 'random':
                    weight = Math.random() * (maxWeight - minWeight) + minWeight;
                    break;
                case 'increment':
                    currentWeight += 0.25;
                    if (currentWeight > maxWeight) {
                        currentWeight = minWeight;
                    }
                    weight = currentWeight;
                    break;
                case 'fixed':
                    weight = (minWeight + maxWeight) / 2;
                    break;
            }

            let prefix;
            if (dataType === 'mixed') {
                prefix = Math.random() > 0.5 ? 'sg' : 'wg';
            } else {
                prefix = dataType;
            }

            const formattedWeight = weight.toFixed(2).padStart(7, '0');
            const dataLine = `${prefix}${formattedWeight}kg`;
            
            addDataLine(dataLine);
            dataCount++;
        }

        function addDataLine(line) {
            const output = document.getElementById('dataOutput');
            const lineElement = document.createElement('div');
            lineElement.className = 'data-line';
            lineElement.textContent = `[${new Date().toLocaleTimeString()}] ${line}`;
            
            output.appendChild(lineElement);
            output.scrollTop = output.scrollHeight;

            // 限制显示的行数，避免内存占用过多
            const lines = output.children;
            if (lines.length > 1000) {
                output.removeChild(lines[0]);
            }
        }

        function clearOutput() {
            document.getElementById('dataOutput').innerHTML = '';
            dataCount = 0;
        }

        function copyOutput() {
            const output = document.getElementById('dataOutput');
            const text = Array.from(output.children).map(line => line.textContent).join('\n');
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    alert('数据已复制到剪贴板');
                }).catch(() => {
                    fallbackCopy(text);
                });
            } else {
                fallbackCopy(text);
            }
        }

        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                alert('数据已复制到剪贴板');
            } catch (err) {
                alert('复制失败，请手动选择并复制数据');
            }
            document.body.removeChild(textArea);
        }

        // 页面关闭时停止生成
        window.addEventListener('beforeunload', () => {
            stopGeneration();
        });
    </script>
</body>
</html>
